<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض الإجابات</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f9f9f9;
            margin: 0;
            padding: 20px;
            direction: ltr;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            border-radius: 10px;
            background-color: #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        h2 {
            background-color: #4CAF50;
            color: #fff;
            padding: 15px;
            text-align: center;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 24px;
        }

        .question {
            border: 1px solid #ddd;
            border-radius: 10px;
            background-color: #fff;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .question p {
            font-size: 18px;
            color: #333;
            margin-bottom: 10px;
        }

        .options {
            list-style: none;
            padding: 0;
        }

        .options li {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            padding: 10px;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }

        .options li.correct {
            background-color: #e7f7e7;
        }

        .options li .circle {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
            font-weight: bold;
            color: #fff;
        }

        .options li.correct .circle {
            background-color: #4CAF50;
        }

        .options li.correct span {
            margin-left: 10px;
            color: #4CAF50;
            font-weight: bold;
        }

        .options li .option-text {
            flex-grow: 1;
        }

        .back-button {
            display: block;
            width: 100%;
            max-width: 200px;
            margin: 20px auto;
            padding: 10px 20px;
            text-align: center;
            background-color: #4CAF50;
            color: #fff;
            text-decoration: none;
            border-radius: 5px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transition: background-color 0.3s ease;
        }

        .back-button:hover {
            background-color: #45a049;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h2>بنك الاسئلة لهذا الامتحان</h2>
        <?php
session_start();
$con = mysqli_connect("localhost", "smartexa_system", "mostafa900", "smartexa_system");

if (isset($_POST['save_multi_select'])) {
    $brands = $_POST['brandlist'];

    $pdo = new PDO("mysql:host=localhost;dbname=smarte10_exam", "smarte10_exam", "mostafa900");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->exec("set names utf8");

    foreach ($brands as $item) {
        $query_nama_ujian = "SELECT nama_ujian FROM mo WHERE id_ujian = ?";
        $stmt_nama_ujian = $pdo->prepare($query_nama_ujian);
        $stmt_nama_ujian->execute([$item]);
        $nama_ujian = $stmt_nama_ujian->fetchColumn();

        if ($stmt_nama_ujian->rowCount() == 0) {
            header("Location: http://smartexams.whf.bz/no.html");
            exit;
        } else {
            echo '<div class="exam-title"><strong>اسم الامتحان: ' . $nama_ujian . '</strong></div>';

            $query = "SELECT * FROM mo WHERE id_ujian = ?";
            $stmt = $pdo->prepare($query);
            $stmt->execute([$item]);
            $row_number = 1;

            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $questions = explode(',', $row['questions']);
                foreach ($questions as $question) {
                    $query_soal = "SELECT * FROM tb_soal WHERE id_soal = ?";
                    $stmt_soal = $pdo->prepare($query_soal);
                    $stmt_soal->execute([$question]);
                    $tb_soal = $stmt_soal->fetch(PDO::FETCH_ASSOC);

                    echo '<div class="question">';
                    echo '<p><strong>رقم السؤال: ' . $row_number++ . '</strong></p>';
                    echo '<p>' . $tb_soal['soal'] . '</p>';
                    echo '<ul class="options">';
                    foreach (['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J'] as $option) {
                        if (!empty($tb_soal[$option])) { // تأكد من أن الخيار ليس فارغاً
                            $is_correct = $tb_soal['jawaban'] == $option;
                            $correct_class = $is_correct ? 'correct' : '';
                            echo '<li class="' . $correct_class . '">';
                            echo '<div class="circle">' . $option . '</div>';
                            echo '<div class="option-text">' . $tb_soal[$option] . '</div>';
                            if ($is_correct) {
                                echo '<span>الاختيار الصحيح</span>';
                            }
                            echo '</li>';
                        }
                    }
                    echo '</ul>';
                    echo '</div>';
                }
            }
        }
    }
}
?>
        <a href="http://smartexams.whf.bz/ujian/examination1" class="back-button">الرجوع</a>
    </div>
</body>
</html>